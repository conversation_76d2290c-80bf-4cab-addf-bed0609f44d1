#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include <filesystem>

namespace zexuan {

Logger::Logger() {
    // 恢复自动初始化功能
    // 现在单例注册表支持注册过程中的递归获取
    init();
}

Logger::~Logger() {
    shutdown();
}

void Logger::init(const std::string& config_path) {
    if (is_initialized_) {
        return;
    }

    try {
        // 使用单例注册表获取ConfigLoader实例
        auto cfg_loader = getSingleton<ConfigLoader>();
        cfg_loader->loadFromFile(config_path);

        // 检查必需的配置节点是否存在
        if (!cfg_loader->exists("logger")) {
            throw std::runtime_error("Logger configuration section not found");
        }

        // 读取全局配置
        log_level_ = cfg_loader->get<std::string>("logger.level", "info");
        log_pattern_ = cfg_loader->get<std::string>("logger.pattern", "[%Y-%m-%d %H:%M:%S.%e] [%n] [%^%l%$] [thread %t] %v");
        log_dir_ = cfg_loader->get<std::string>("logger.log_dir", "logs");
        max_file_size_ = cfg_loader->get<size_t>("logger.max_file_size", 5 * 1024 * 1024);
        max_files_ = cfg_loader->get<size_t>("logger.max_files", 3);
        is_async_ = cfg_loader->get<bool>("logger.async", false);

        // 创建日志目录
        std::filesystem::create_directories(log_dir_);

        // 如果是异步模式，初始化线程池
        if (is_async_) {
            size_t queue_size = cfg_loader->get<size_t>("logger.async_queue_size", 8192);
            size_t thread_count = cfg_loader->get<size_t>("logger.thread_count", 1);
            spdlog::init_thread_pool(queue_size, thread_count);
        }

        // 创建默认的控制台logger
        default_logger_ = spdlog::stdout_color_mt("console");
        default_logger_->set_pattern(log_pattern_);
        
        // 设置日志级别
        if (log_level_ == "trace") default_logger_->set_level(spdlog::level::trace);
        else if (log_level_ == "debug") default_logger_->set_level(spdlog::level::debug);
        else if (log_level_ == "info") default_logger_->set_level(spdlog::level::info);
        else if (log_level_ == "warn") default_logger_->set_level(spdlog::level::warn);
        else if (log_level_ == "error") default_logger_->set_level(spdlog::level::err);
        else if (log_level_ == "critical") default_logger_->set_level(spdlog::level::critical);
        else if (log_level_ == "off") default_logger_->set_level(spdlog::level::off);
        else throw std::runtime_error("Invalid log level: " + log_level_);

        spdlog::set_default_logger(default_logger_);
        is_initialized_ = true;

    } catch (const ConfigLoader::ConfigError& e) {
        throw std::runtime_error(std::string("Failed to load logger configuration: ") + e.what());
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("Logger initialization failed: ") + e.what());
    }
}

std::shared_ptr<spdlog::logger> Logger::getLogger(const std::string& name) {
    // 先查找是否已存在
    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }

    // 不存在则创建新的文件logger
    auto logger = createFileLogger(name);
    loggers_[name] = logger;
    return logger;
}

std::shared_ptr<spdlog::logger> Logger::createFileLogger(const std::string& name)
{
    try {
        // 构建文件名
        std::string filename = log_dir_ + "/" + name + ".log";

        // 创建文件sink，使用成员变量中的配置
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            filename, max_file_size_, max_files_);

        std::shared_ptr<spdlog::logger> logger;
        if (is_async_) {
            // 创建异步logger
            logger = std::make_shared<spdlog::async_logger>(
                name,
                file_sink,
                spdlog::thread_pool(),
                spdlog::async_overflow_policy::block
            );
        } else {
            // 创建同步logger
            logger = std::make_shared<spdlog::logger>(name, file_sink);
        }

        // 设置日志格式和级别
        logger->set_pattern(log_pattern_);
        if (log_level_ == "trace") logger->set_level(spdlog::level::trace);
        else if (log_level_ == "debug") logger->set_level(spdlog::level::debug);
        else if (log_level_ == "info") logger->set_level(spdlog::level::info);
        else if (log_level_ == "warn") logger->set_level(spdlog::level::warn);
        else if (log_level_ == "error") logger->set_level(spdlog::level::err);
        else if (log_level_ == "critical") logger->set_level(spdlog::level::critical);
        else if (log_level_ == "off") logger->set_level(spdlog::level::off);

        // 注册logger
        spdlog::register_logger(logger);
        return logger;

    } catch (const spdlog::spdlog_ex& ex) {
        if (default_logger_) {
            default_logger_->error("Failed to create logger: {}", ex.what());
        }
        throw std::runtime_error(std::string("Failed to create logger: ") + ex.what());
    }
}

void Logger::shutdown() {
    // 刷新并关闭所有logger
    for (auto& pair : loggers_) {
        if (pair.second) {
            pair.second->flush();
        }
    }
    if (default_logger_) {
        default_logger_->flush();
    }
    spdlog::shutdown();
}

} // namespace zexuan
