#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include "zexuan/base/singleton.hpp"
#include "zexuan/base/singleton_registry.hpp"

namespace zexuan {

class Logger : public singleton<Logger> {
private:
    Logger();
    ~Logger();

    friend class singleton<Logger>;

public:
    // 获取默认的控制台logger
    std::shared_ptr<spdlog::logger> getDefaultLogger() const { return default_logger_; }

    // 获取或创建一个文件logger
    std::shared_ptr<spdlog::logger> getLogger(const std::string& name);

    // 初始化默认logger和全局配置
    void init(const std::string& config_path = "config/config.json");

    // 关闭所有logger
    void shutdown();

private:
    // 创建一个新的文件logger（简化版本，只需要name参数）
    std::shared_ptr<spdlog::logger> createFileLogger(const std::string& name);

private:
    std::shared_ptr<spdlog::logger> default_logger_{nullptr};  // 默认的控制台logger
    std::unordered_map<std::string, std::shared_ptr<spdlog::logger>> loggers_;  // 文件logger映射
    bool is_async_{false};
    size_t max_file_size_{5 * 1024 * 1024};
    size_t max_files_{3};
    std::string log_level_{"info"};
    std::string log_pattern_{"[%Y-%m-%d %H:%M:%S.%e] [%n] [%^%l%$] [thread %t] %v"};
    std::string log_dir_{"logs"};
    bool is_initialized_{false};

    // 静态标志，跟踪线程池初始化状态
    static bool thread_pool_initialized_;
};

} // namespace zexuan
