#pragma once

#include "zexuan/base/singleton.hpp"
#include "zexuan/base/noncopyable.hpp"
#include <memory>
#include <unordered_map>
#include <typeindex>
#include <string>
#include <stdexcept>
#include <mutex>

namespace zexuan {

/**
 * 单例注册表异常
 */
class RegistryError : public std::runtime_error {
public:
    using std::runtime_error::runtime_error;
};

/**
 * 简单的单例注册表（惰性初始化模式）
 *
 * 功能：
 * - 集中管理现有单例的访问
 * - 提供统一的获取接口
 * - 线程安全
 * - 轻量级，无复杂依赖注入
 * - 惰性初始化：注册时不创建实例，首次访问时才构造
 * - 使用 Schwarz Counter 模式保证全局唯一性
 *
 * 使用示例：
 * @code
 * // 注册现有单例
 * registry.registerSingleton<Logger>();
 * registry.registerSingleton<ConfigLoader>();
 *
 * // 统一访问
 * auto logger = registry.get<Logger>();
 * auto config = registry.get<ConfigLoader>();
 * @endcode
 */
class SingletonRegistry {
public:
    /**
     * 获取全局唯一的注册表实例
     * 使用 Schwarz Counter 模式保证全局唯一性
     */
    static SingletonRegistry& getInstance() {
        static SingletonRegistry instance;
        return instance;
    }

    /**
     * 注册现有单例类型（惰性初始化模式）
     * @tparam T 单例类型（必须继承自singleton<T>）
     */
    template<typename T>
    void registerSingleton() {
        static_assert(std::is_base_of_v<singleton<T>, T>,
                     "T must inherit from singleton<T>");

        std::string key = typeid(T).name();
        std::lock_guard<std::mutex> lock(mutex_);

        // 只注册类型，不创建实例（惰性初始化）
        if (singletons_.count(key) == 0) {
            // 注册占位符，但不创建实例
            singletons_[key] = nullptr;
        }
    }

    /**
     * 获取单例实例（惰性初始化）
     * @tparam T 单例类型
     * @return 单例实例
     */
    template<typename T>
    std::shared_ptr<T> get() {
        std::string key = typeid(T).name();
        std::lock_guard<std::mutex> lock(mutex_);

        auto it = singletons_.find(key);
        if (it == singletons_.end()) {
            throw RegistryError("Singleton not registered: " + std::string(typeid(T).name()));
        }

        // 惰性初始化：第一次访问时才构造实例
        if (!it->second) {
            it->second = std::shared_ptr<T>(&T::getInstance(), [](T*) {});
        }

        return std::static_pointer_cast<T>(it->second);
    }
    
    /**
     * 尝试获取单例实例（不抛出异常）
     * @tparam T 单例类型
     * @return 单例实例，如果不存在则返回nullptr
     */
    template<typename T>
    std::shared_ptr<T> tryGet() {
        try {
            return get<T>();
        } catch (const RegistryError&) {
            return nullptr;
        }
    }
    
    /**
     * 检查单例是否已注册
     * @tparam T 单例类型
     * @return 是否已注册
     */
    template<typename T>
    bool contains() const {
        std::lock_guard<std::mutex> lock(mutex_);
        std::string key = typeid(T).name();
        return singletons_.find(key) != singletons_.end();
    }
    
    /**
     * 获取已注册的单例数量
     */
    size_t size() const {
        std::lock_guard<std::recursive_mutex> lock(mutex_);
        return singletons_.size();
    }

    /**
     * 清除所有注册（谨慎使用）
     */
    void clear() {
        std::lock_guard<std::recursive_mutex> lock(mutex_);
        singletons_.clear();
        registering_.clear();
    }

private:
    // 私有构造函数，防止外部实例化
    SingletonRegistry() = default;
    ~SingletonRegistry() = default;

    // 禁止拷贝和移动
    SingletonRegistry(const SingletonRegistry&) = delete;
    SingletonRegistry& operator=(const SingletonRegistry&) = delete;
    SingletonRegistry(SingletonRegistry&&) = delete;
    SingletonRegistry& operator=(SingletonRegistry&&) = delete;

    // 单例存储
    std::unordered_map<std::string, std::shared_ptr<void>> singletons_;

    // 正在注册的单例标记（避免重复注册）
    std::unordered_set<std::string> registering_;

    // 线程安全（使用递归锁支持递归调用）
    mutable std::recursive_mutex mutex_;
};

// === 跨动态库单例访问 ===

/**
 * 单例访问函数指针类型
 */
using SingletonRegistryAccessor = SingletonRegistry& (*)();

/**
 * 全局单例注册表访问器指针
 * 由主程序设置，插件通过此指针访问主程序的单例注册表
 */
extern SingletonRegistryAccessor g_singleton_registry_accessor;

/**
 * 设置单例注册表访问器（仅主程序调用）
 */
inline void setSingletonRegistryAccessor(SingletonRegistryAccessor accessor) {
    g_singleton_registry_accessor = accessor;
}

/**
 * 获取单例注册表实例
 * 支持跨动态库访问
 */
inline SingletonRegistry& singleton_registry() {
    if (g_singleton_registry_accessor) {
        // 插件环境：使用主程序提供的访问器
        return g_singleton_registry_accessor();
    } else {
        // 主程序环境：直接访问本地实例
        return SingletonRegistry::getInstance();
    }
}

/**
 * 便捷的单例获取函数
 */
template<typename T>
std::shared_ptr<T> getSingleton() {
    return singleton_registry().get<T>();
}

/**
 * 便捷的单例注册函数
 */
template<typename T>
void registerSingleton() {
    singleton_registry().registerSingleton<T>();
}



} // namespace zexuan
