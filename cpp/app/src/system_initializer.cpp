#include "system_initializer.hpp"

namespace zexuan {
// 注意：g_singleton_registry_accessor 现在在 core/src/zexuan/singleton_registry.cpp 中定义
void SystemInitializer::initialize() {
    auto& registry = singleton_registry();

    // 注册核心单例
    registry.registerSingleton<ConfigLoader>();
    registry.registerSingleton<Logger>();
    // registry.registerSingleton<plugin::MessageMediator>();
    // registry.registerSingleton<plugin::SimplePluginManager>();

    std::cout << "SystemInitializer: 所有单例已注册" << std::endl;

    // 显式初始化Logger系统（避免循环依赖）
    std::cout << "SystemInitializer: 正在初始化Logger系统..." << std::endl;
    auto logger = getSingleton<Logger>();
    logger->init("config/config.json");
    std::cout << "SystemInitializer: Logger系统初始化完成" << std::endl;

    // // 初始化插件管理器
    // std::cout << "SystemInitializer: 正在初始化插件管理器..." << std::endl;
    // if (!plugin::api::initialize()) {
    //     std::cerr << "SystemInitializer: 插件管理器初始化失败: " << plugin::api::getLastError() << std::endl;
    //     throw std::runtime_error("插件管理器初始化失败");
    // }

    // // 启动插件管理器
    // if (!plugin::api::start()) {
    //     std::cerr << "SystemInitializer: 插件管理器启动失败: " << plugin::api::getLastError() << std::endl;
    //     throw std::runtime_error("插件管理器启动失败");
    // }

    // std::cout << "SystemInitializer: 插件管理器已初始化并启动" << std::endl;

    // // 显示插件管理器状态
    // displayPluginManagerStatus();

    // // 加载配置文件中定义的插件
    // loadPluginsFromConfig();

    // // 显示插件加载结果
    // displayPluginLoadResults();

    // // 验证通信系统
    // verifyPluginCommunication();

    std::cout << "SystemInitializer: 系统初始化完成" << std::endl;
}

void SystemInitializer::cleanup() {
    // std::cout << "SystemInitializer: 开始清理..." << std::endl;

    // try {
    //     // 先关闭插件管理器
    //     std::cout << "SystemInitializer: 正在关闭插件管理器..." << std::endl;

    //     // 停止插件管理器
    //     if (!plugin::api::stop()) {
    //         std::cout << "SystemInitializer: 插件管理器停止时出现问题: " << plugin::api::getLastError() << std::endl;
    //     } else {
    //         std::cout << "SystemInitializer: 插件管理器已停止" << std::endl;
    //     }

    //     // 关闭所有插件
    //     if (!plugin::api::shutdown()) {
    //         std::cout << "SystemInitializer: 插件关闭时出现问题: " << plugin::api::getLastError() << std::endl;
    //     } else {
    //         std::cout << "SystemInitializer: 所有插件已关闭" << std::endl;
    //     }

    //     // 最后清理单例注册表（Logger 等核心服务最后清理）
    //     std::cout << "SystemInitializer: 正在清理单例注册表..." << std::endl;
    //     singleton_registry().clear();
    //     std::cout << "SystemInitializer: 所有单例已清理" << std::endl;

    // } catch (const std::exception& e) {
    //     std::cerr << "SystemInitializer: 清理过程中发生异常: " << e.what() << std::endl;
    // } catch (...) {
    //     std::cerr << "SystemInitializer: 清理过程中发生未知异常" << std::endl;
    // }

    // std::cout << "SystemInitializer: 清理完成" << std::endl;
}

void SystemInitializer::displayPluginManagerStatus() {
    // std::cout << "SystemInitializer: 检查插件管理器状态..." << std::endl;

    // auto managerState = plugin::api::getManagerState();
    // std::cout << "   管理器状态: ";
    // switch (managerState) {
    //     case plugin::PluginManagerState::RUNNING:
    //         std::cout << "运行中" << std::endl;
    //         break;
    //     case plugin::PluginManagerState::INITIALIZED:
    //         std::cout << "已初始化" << std::endl;
    //         break;
    //     case plugin::PluginManagerState::STOPPED:
    //         std::cout << "已停止" << std::endl;
    //         break;
    //     case plugin::PluginManagerState::ERROR:
    //         std::cout << "错误状态" << std::endl;
    //         break;
    //     default:
    //         std::cout << "未知状态" << std::endl;
    //         break;
    // }
}

void SystemInitializer::loadPluginsFromConfig() {
    // std::cout << "SystemInitializer: 从配置文件加载插件..." << std::endl;

    // try {
    //     // 获取ConfigLoader单例
    //     auto configLoader = getSingleton<ConfigLoader>();

    //     // 尝试加载配置文件
    //     const std::string configPath = "config/config.json";
    //     std::cout << "尝试加载配置文件: " << configPath << std::endl;

    //     try {
    //         configLoader->loadFromFile(configPath);
    //     } catch (const ConfigLoader::ConfigError& e) {
    //         std::cout << "配置文件加载失败: " << e.what() << std::endl;
    //     }

    //     // 从配置中获取插件列表
    //     auto pluginPaths = configLoader->get<std::vector<std::string>>("plugins", std::vector<std::string>{"libs/plugins/libcomic_plugin.so"});

    //     std::cout << "发现 " << pluginPaths.size() << " 个插件需要加载:" << std::endl;

    //     // 加载每个插件
    //     for (const auto& pluginPath : pluginPaths) {
    //         std::cout << "正在加载插件: " << pluginPath << std::endl;

    //         if (plugin::api::loadPlugin(pluginPath)) {
    //             std::cout << "插件加载成功" << std::endl;
    //         } else {
    //             std::cout << "插件加载失败: " << plugin::api::getLastError() << std::endl;
    //             // 继续加载其他插件，不中断整个过程
    //         }
    //     }

    // } catch (const std::exception& e) {
    //     std::cerr << "   插件加载过程中发生异常: " << e.what() << std::endl;
    //     std::cerr << "   尝试加载默认插件..." << std::endl;

    //     // 作为后备方案，尝试加载默认插件
    //     const std::string defaultPlugin = "libs/plugins/libcomic_plugin.so";
    //     if (plugin::api::loadPlugin(defaultPlugin)) {
    //         std::cout << "默认插件加载成功: " << defaultPlugin << std::endl;
    //     } else {
    //         std::cout << "默认插件加载失败: " << plugin::api::getLastError() << std::endl;
    //     }
    // }
}

void SystemInitializer::displayPluginLoadResults() {
    // std::cout << "SystemInitializer: 显示插件加载结果..." << std::endl;

    // auto allPlugins = plugin::api::getAllPluginInfos();
    // if (allPlugins.empty()) {
    //     std::cout << "没有成功加载任何插件" << std::endl;
    //     return;
    // }

    // std::cout << "   成功加载 " << allPlugins.size() << " 个插件:" << std::endl;
    // for (const auto& info : allPlugins) {
    //     std::cout << "   📦 插件: " << info.name << std::endl;
    //     std::cout << "      版本: " << info.version << std::endl;
    //     std::cout << "      描述: " << info.description << std::endl;
    //     std::cout << "      路径: " << info.libPath << std::endl;
    //     std::cout << "      状态: ";

    //     switch (info.state) {
    //         case plugin::PluginState::RUNNING:
    //             std::cout << "运行中";
    //             break;
    //         case plugin::PluginState::INITIALIZED:
    //             std::cout << "已初始化";
    //             break;
    //         case plugin::PluginState::LOADED:
    //             std::cout << "已加载";
    //             break;
    //         case plugin::PluginState::ERROR:
    //             std::cout << "错误状态";
    //             break;
    //         default:
    //             std::cout << "未知状态";
    //             break;
    //     }
    //     std::cout << std::endl << std::endl;
    // }
}

void SystemInitializer::verifyPluginCommunication() {
    // std::cout << "SystemInitializer: 验证插件通信系统..." << std::endl;

    // // 显示MessageMediator地址
    // auto mainMediator = getSingleton<plugin::MessageMediator>();
    // std::cout << "   MessageMediator 地址: " << mainMediator.get() << std::endl;

    // // 检查已注册的通信参与者
    // auto registeredParticipants = plugin::api::getRegisteredPluginNames();
    // std::cout << "   已注册的通信参与者: ";
    // for (const auto& participant : registeredParticipants) {
    //     std::cout << participant << " ";
    // }
    // std::cout << std::endl;

    // if (registeredParticipants.empty()) {
    //     std::cout << "警告: 没有插件注册到通信系统，可能存在跨库单例问题" << std::endl;
    // } else {
    //     std::cout << "通信系统正常，共有 " << registeredParticipants.size() << " 个参与者" << std::endl;
    // }
}

} // namespace zexuan
