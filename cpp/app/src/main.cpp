/**
 * @file main.cpp
 * @brief Logger 测试主程序
 * @date 2024
 *
 * 这个程序专门用于测试 Logger 系统的功能：
 * 1. 使用 SystemInitializer 初始化系统
 * 2. 测试简化后的 Logger 接口
 * 3. 测试异步日志功能
 */

#include <iostream>
#include <memory>
#include <string>
#include <filesystem>
#include <thread>
#include <chrono>

// 使用系统初始化器
#include "system_initializer.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"

void printSeparator(const std::string& title) {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << " " << title << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        printSeparator("Logger 系统测试程序");

        // 使用 SystemInitializer 初始化系统
        printSeparator("系统初始化");
        std::cout << "1. 初始化系统..." << std::endl;
        zexuan::SystemInitializer::initialize();
        std::cout << "   ✅ 系统初始化完成" << std::endl;

        // 2. 获取Logger实例并初始化
        printSeparator("Logger 初始化");
        std::cout << "2. 初始化Logger系统..." << std::endl;

        auto& logger_instance = zexuan::Logger::getInstance();
        logger_instance.init("config/config.json");
        std::cout << "   ✅ Logger系统初始化完成（使用异步模式）" << std::endl;

        // 3. 测试默认控制台Logger
        printSeparator("控制台Logger测试");
        std::cout << "3. 测试默认控制台Logger..." << std::endl;

        auto console_logger = logger_instance.getDefaultLogger();
        console_logger->info("这是一条信息日志");
        console_logger->debug("这是一条调试日志");
        console_logger->warn("这是一条警告日志");
        console_logger->error("这是一条错误日志");
        std::cout << "   ✅ 控制台Logger测试完成" << std::endl;

        // 4. 测试简化的文件Logger创建（只需要name参数）
        printSeparator("文件Logger测试");
        std::cout << "4. 测试简化的文件Logger创建..." << std::endl;

        // 创建应用Logger（只需要name参数，其他配置从config.json读取）
        auto app_logger = logger_instance.getLogger("app");
        app_logger->info("应用Logger创建成功 - 使用简化接口");
        app_logger->debug("这是应用的调试信息");
        app_logger->warn("这是应用的警告信息");

        // 创建模块Logger
        auto module_logger = logger_instance.getLogger("module");
        module_logger->info("模块Logger创建成功 - 配置从config.json读取");
        module_logger->error("这是模块的错误信息");

        // 创建数据库Logger
        auto db_logger = logger_instance.getLogger("database");
        db_logger->info("数据库Logger创建成功 - 文件名自动生成");
        db_logger->debug("数据库连接信息");

        std::cout << "   ✅ 文件Logger创建测试完成" << std::endl;

        // 5. 测试Logger实例复用
        printSeparator("Logger实例复用测试");
        std::cout << "5. 测试Logger实例复用..." << std::endl;

        // 再次获取相同名称的Logger，应该返回同一实例
        auto app_logger_again = logger_instance.getLogger("app");
        if (app_logger == app_logger_again) {
            std::cout << "   ✅ 相同名称的Logger返回同一实例" << std::endl;
        } else {
            std::cout << "   ❌ 相同名称的Logger返回不同实例" << std::endl;
        }

        app_logger_again->info("这是复用的Logger实例写入的日志");
        std::cout << "   ✅ Logger实例复用测试完成" << std::endl;

        // 6. 检查日志文件创建
        printSeparator("日志文件检查");
        std::cout << "6. 检查日志文件创建..." << std::endl;

        // 检查日志文件是否创建
        std::vector<std::string> expected_files = {"app.log", "module.log", "database.log"};
        for (const auto& filename : expected_files) {
            std::string filepath = "logs/" + filename;
            if (std::filesystem::exists(filepath)) {
                std::cout << "   ✅ 日志文件创建成功: " << filepath << std::endl;
            } else {
                std::cout << "   ❌ 日志文件未找到: " << filepath << std::endl;
            }
        }

        // 7. 异步日志性能测试
        printSeparator("异步日志性能测试");
        std::cout << "7. 测试异步日志性能..." << std::endl;

        auto perf_logger = logger_instance.getLogger("performance");
        auto start_time = std::chrono::high_resolution_clock::now();

        // 写入大量日志测试异步性能
        for (int i = 0; i < 1000; ++i) {
            perf_logger->info("性能测试日志 #{} - 异步模式", i);
        }

        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

        std::cout << "   ✅ 写入1000条日志耗时: " << duration.count() << "ms" << std::endl;
        std::cout << "   ✅ 异步日志性能测试完成" << std::endl;

        // 8. 测试总结
        printSeparator("测试总结");
        std::cout << "8. Logger系统测试总结:" << std::endl;
        std::cout << "   ✅ 系统初始化成功" << std::endl;
        std::cout << "   ✅ Logger系统初始化成功（异步模式）" << std::endl;
        std::cout << "   ✅ 使用单例注册表获取ConfigLoader" << std::endl;
        std::cout << "   ✅ 简化的createFileLogger接口测试完成" << std::endl;
        std::cout << "   ✅ Logger实例复用功能正常" << std::endl;
        std::cout << "   ✅ 异步日志性能测试完成" << std::endl;

        std::cout << "\n💡 Logger系统更新说明:" << std::endl;
        std::cout << "   - 默认使用异步日志模式，提高性能" << std::endl;
        std::cout << "   - createFileLogger只需要name参数，配置从config.json读取" << std::endl;
        std::cout << "   - 使用单例注册表统一管理ConfigLoader依赖" << std::endl;
        std::cout << "   - 文件名自动生成：logs/{name}.log" << std::endl;
        std::cout << "   - 所有配置集中在config.json中管理" << std::endl;

        printSeparator("程序完成");
        std::cout << "🎉 Logger系统测试完成！" << std::endl;

        // 关闭Logger系统
        logger_instance.shutdown();

        // 清理系统
        zexuan::SystemInitializer::cleanup();
        std::cout << "\n📝 注意: 系统资源已清理完成" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "\n❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
