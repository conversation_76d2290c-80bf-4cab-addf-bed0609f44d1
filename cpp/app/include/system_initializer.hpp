#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
// #include "zexuan/plugin/message_mediator.hpp"
// #include "zexuan/plugin_manager.hpp"
// #include "zexuan/plugin/plugin_communication.hpp"
#include <thread>
#include <chrono>
#include <iostream>

namespace zexuan{
/**
 * 系统初始化器 - 自动注册核心单例
 */
class SystemInitializer {
public:
    /**
     * 初始化系统单例注册表
     */
    static void initialize();

    /**
     * 清理系统
     */
    static void cleanup();

private:
    /**
     * 显示插件管理器状态
     */
    static void displayPluginManagerStatus();

    /**
     * 从配置文件加载插件
     */
    static void loadPluginsFromConfig();

    /**
     * 显示插件加载结果
     */
    static void displayPluginLoadResults();

    /**
     * 验证插件通信系统
     */
    static void verifyPluginCommunication();
};

/**
 * RAII风格的系统管理器
 */
class SystemManager {
public:
    SystemManager() {
        SystemInitializer::initialize();
    }
    
    ~SystemManager() {
        SystemInitializer::cleanup();
    }
    
    // 禁止拷贝和移动
    SystemManager(const SystemManager&) = delete;
    SystemManager& operator=(const SystemManager&) = delete;
    SystemManager(SystemManager&&) = delete;
    SystemManager& operator=(SystemManager&&) = delete;
};
}