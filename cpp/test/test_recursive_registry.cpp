#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <iostream>

using namespace zexuan;

int main() {
    try {
        std::cout << "=== 递归安全单例注册表测试 ===" << std::endl;
        
        // 1. 测试注册过程中的递归获取
        std::cout << "1. 测试注册过程中的递归获取..." << std::endl;
        
        // 注册ConfigLoader
        std::cout << "   注册ConfigLoader..." << std::endl;
        registerSingleton<ConfigLoader>();
        std::cout << "   ✅ ConfigLoader注册完成" << std::endl;
        
        // 注册Logger（会在构造函数中自动初始化，触发递归获取ConfigLoader）
        std::cout << "   注册Logger（会触发自动初始化）..." << std::endl;
        registerSingleton<Logger>();
        std::cout << "   ✅ Logger注册并自动初始化完成" << std::endl;
        
        // 2. 测试正常获取功能
        std::cout << "2. 测试正常获取功能..." << std::endl;
        
        auto config_loader = getSingleton<ConfigLoader>();
        std::cout << "   ✅ ConfigLoader获取成功: " << config_loader.get() << std::endl;
        
        auto logger = getSingleton<Logger>();
        std::cout << "   ✅ Logger获取成功: " << logger.get() << std::endl;
        
        // 3. 测试Logger功能
        std::cout << "3. 测试Logger功能..." << std::endl;
        
        auto console_logger = logger->getDefaultLogger();
        console_logger->info("递归安全注册表测试：Logger自动初始化成功");
        
        auto file_logger = logger->getLogger("recursive_test");
        file_logger->info("递归安全注册表测试：文件Logger创建成功");
        
        std::cout << "   ✅ Logger功能测试完成" << std::endl;
        
        // 4. 测试重复注册
        std::cout << "4. 测试重复注册..." << std::endl;
        
        registerSingleton<Logger>();  // 应该不会重复注册
        auto logger2 = getSingleton<Logger>();
        
        if (logger.get() == logger2.get()) {
            std::cout << "   ✅ 重复注册返回同一实例" << std::endl;
        } else {
            std::cout << "   ❌ 重复注册返回不同实例" << std::endl;
        }
        
        // 5. 清理
        logger->shutdown();
        std::cout << "5. ✅ Logger系统已关闭" << std::endl;
        
        std::cout << "\n=== 递归安全注册表测试成功 ===" << std::endl;
        std::cout << "关键特性验证：" << std::endl;
        std::cout << "- ✅ 支持注册过程中的递归获取" << std::endl;
        std::cout << "- ✅ 使用递归锁避免死锁" << std::endl;
        std::cout << "- ✅ 防止重复注册" << std::endl;
        std::cout << "- ✅ Logger自动初始化功能恢复" << std::endl;
        std::cout << "- ✅ 保持原有的所有功能" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
