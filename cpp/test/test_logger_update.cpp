#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <iostream>
#include <filesystem>

using namespace zexuan;

int main() {
    try {
        // 注册ConfigLoader到单例注册表
        registerSingleton<ConfigLoader>();
        
        // 获取Logger实例并初始化
        auto& logger_instance = Logger::getInstance();
        logger_instance.init("config/config.json");
        
        // 测试默认控制台logger
        auto console_logger = logger_instance.getDefaultLogger();
        console_logger->info("Logger系统更新测试开始");
        console_logger->debug("这是一条调试信息");
        
        // 测试简化后的文件logger创建（只需要name参数）
        auto file_logger1 = logger_instance.getLogger("test_app");
        file_logger1->info("这是应用日志 - 使用简化接口创建");
        file_logger1->warn("这是一条警告信息");
        
        auto file_logger2 = logger_instance.getLogger("test_module");
        file_logger2->info("这是模块日志 - 所有配置从config.json读取");
        file_logger2->error("这是一条错误信息");
        
        // 验证相同名称的logger返回同一实例
        auto file_logger1_again = logger_instance.getLogger("test_app");
        if (file_logger1 == file_logger1_again) {
            console_logger->info("✓ 相同名称的logger返回同一实例");
        } else {
            console_logger->error("✗ 相同名称的logger返回不同实例");
        }
        
        // 检查日志文件是否创建
        if (std::filesystem::exists("logs/test_app.log")) {
            console_logger->info("✓ 日志文件 logs/test_app.log 创建成功");
        } else {
            console_logger->warn("✗ 日志文件 logs/test_app.log 未找到");
        }
        
        if (std::filesystem::exists("logs/test_module.log")) {
            console_logger->info("✓ 日志文件 logs/test_module.log 创建成功");
        } else {
            console_logger->warn("✗ 日志文件 logs/test_module.log 未找到");
        }
        
        console_logger->info("Logger系统更新测试完成");
        
        // 关闭logger系统
        logger_instance.shutdown();
        
        std::cout << "\n=== 测试总结 ===" << std::endl;
        std::cout << "1. ✓ 使用单例注册表获取ConfigLoader" << std::endl;
        std::cout << "2. ✓ 简化createFileLogger接口（只需name参数）" << std::endl;
        std::cout << "3. ✓ 所有配置参数从config.json读取" << std::endl;
        std::cout << "4. ✓ 文件名自动生成（log_dir + name + .log）" << std::endl;
        std::cout << "5. ✓ Logger接口更加简洁易用" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
