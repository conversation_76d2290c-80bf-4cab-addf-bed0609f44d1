#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <iostream>
#include <thread>
#include <chrono>

using namespace zexuan;

int main() {
    try {
        std::cout << "=== 死锁修复验证测试 ===" << std::endl;
        
        std::cout << "\n【问题分析】" << std::endl;
        std::cout << "之前的实现会在以下流程中死锁：" << std::endl;
        std::cout << "1. getSingleton<Logger>() 获取mutex锁" << std::endl;
        std::cout << "2. 在锁内调用 Logger::getInstance() → Logger::init()" << std::endl;
        std::cout << "3. Logger::init() 调用 getSingleton<ConfigLoader>()" << std::endl;
        std::cout << "4. 尝试获取已被持有的锁 → 死锁" << std::endl;
        
        std::cout << "\n【修复方案】" << std::endl;
        std::cout << "使用双重检查锁定模式：" << std::endl;
        std::cout << "1. 在锁内检查是否需要构造" << std::endl;
        std::cout << "2. 释放锁，在锁外构造实例" << std::endl;
        std::cout << "3. 重新获取锁，存储实例" << std::endl;
        
        // 1. 注册单例
        std::cout << "\n1. 注册单例..." << std::endl;
        registerSingleton<ConfigLoader>();
        registerSingleton<Logger>();
        std::cout << "   ✅ 注册完成（惰性模式，未构造实例）" << std::endl;
        
        // 2. 测试Logger的获取（会触发循环依赖）
        std::cout << "2. 测试Logger获取（触发循环依赖）..." << std::endl;
        
        auto start_time = std::chrono::high_resolution_clock::now();
        
        std::cout << "   正在获取Logger实例..." << std::endl;
        auto logger = getSingleton<Logger>();
        
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
        
        std::cout << "   ✅ Logger获取成功，耗时: " << duration.count() << "ms" << std::endl;
        std::cout << "   ✅ 无死锁发生！" << std::endl;
        
        // 3. 验证Logger功能
        std::cout << "3. 验证Logger功能..." << std::endl;
        
        auto console_logger = logger->getDefaultLogger();
        console_logger->info("死锁修复测试：Logger功能正常");
        
        auto file_logger = logger->getLogger("deadlock_test");
        file_logger->info("死锁修复测试：文件Logger创建成功");
        
        std::cout << "   ✅ Logger功能正常" << std::endl;
        
        // 4. 测试ConfigLoader
        std::cout << "4. 测试ConfigLoader..." << std::endl;
        
        auto config_loader = getSingleton<ConfigLoader>();
        std::cout << "   ✅ ConfigLoader获取成功: " << config_loader.get() << std::endl;
        
        // 5. 验证单例唯一性
        std::cout << "5. 验证单例唯一性..." << std::endl;
        
        auto logger2 = getSingleton<Logger>();
        auto config_loader2 = getSingleton<ConfigLoader>();
        
        if (logger.get() == logger2.get()) {
            std::cout << "   ✅ Logger单例唯一性保证" << std::endl;
        } else {
            std::cout << "   ❌ Logger单例唯一性失败" << std::endl;
        }
        
        if (config_loader.get() == config_loader2.get()) {
            std::cout << "   ✅ ConfigLoader单例唯一性保证" << std::endl;
        } else {
            std::cout << "   ❌ ConfigLoader单例唯一性失败" << std::endl;
        }
        
        // 6. 多线程测试
        std::cout << "6. 多线程并发测试..." << std::endl;
        
        std::vector<std::thread> threads;
        std::atomic<int> success_count{0};
        
        for (int i = 0; i < 10; ++i) {
            threads.emplace_back([&success_count]() {
                try {
                    auto logger = getSingleton<Logger>();
                    auto config = getSingleton<ConfigLoader>();
                    success_count++;
                } catch (const std::exception& e) {
                    std::cerr << "线程异常: " << e.what() << std::endl;
                }
            });
        }
        
        for (auto& t : threads) {
            t.join();
        }
        
        std::cout << "   ✅ 多线程测试完成，成功: " << success_count.load() << "/10" << std::endl;
        
        // 7. 清理
        logger->shutdown();
        std::cout << "7. ✅ 清理完成" << std::endl;
        
        std::cout << "\n=== 死锁修复验证成功 ===" << std::endl;
        std::cout << "🎯 修复要点：" << std::endl;
        std::cout << "- ✅ 使用双重检查锁定模式" << std::endl;
        std::cout << "- ✅ 在锁外构造实例，避免死锁" << std::endl;
        std::cout << "- ✅ 保持线程安全和单例唯一性" << std::endl;
        std::cout << "- ✅ 支持循环依赖的单例构造" << std::endl;
        std::cout << "- ✅ 多线程环境下稳定工作" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
