#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <iostream>

using namespace zexuan;

int main() {
    try {
        std::cout << "=== Logger循环依赖修复测试 ===" << std::endl;
        
        // 1. 注册单例（模拟SystemInitializer的行为）
        std::cout << "1. 注册单例到注册表..." << std::endl;
        registerSingleton<ConfigLoader>();
        std::cout << "   ✅ ConfigLoader已注册" << std::endl;
        
        registerSingleton<Logger>();
        std::cout << "   ✅ Logger已注册（不会触发自动初始化）" << std::endl;
        
        // 2. 显式初始化Logger
        std::cout << "2. 显式初始化Logger..." << std::endl;
        auto logger = getSingleton<Logger>();
        logger->init("config/config.json");
        std::cout << "   ✅ Logger初始化完成" << std::endl;
        
        // 3. 测试Logger功能
        std::cout << "3. 测试Logger功能..." << std::endl;
        auto console_logger = logger->getDefaultLogger();
        console_logger->info("测试信息：Logger修复成功");
        
        auto file_logger = logger->getLogger("test_fix");
        file_logger->info("测试文件Logger：循环依赖已解决");
        
        std::cout << "   ✅ Logger功能测试完成" << std::endl;
        
        // 4. 清理
        logger->shutdown();
        std::cout << "4. ✅ Logger系统已关闭" << std::endl;
        
        std::cout << "\n=== 修复验证成功 ===" << std::endl;
        std::cout << "修复要点：" << std::endl;
        std::cout << "- Logger构造函数不再自动调用init()" << std::endl;
        std::cout << "- SystemInitializer显式初始化Logger" << std::endl;
        std::cout << "- 避免了注册时的循环依赖问题" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
