#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <iostream>

using namespace zexuan;

/**
 * 跨动态库单例管理测试
 * 
 * 正确的使用模式：
 * 1. 主程序负责注册所有需要跨库共享的单例
 * 2. 插件通过 singleton_registry() 访问主程序的注册表
 * 3. 确保所有库使用同一个注册表实例
 */

void demonstrateCorrectUsage() {
    std::cout << "=== 跨动态库单例正确使用模式 ===" << std::endl;
    
    // 1. 主程序启动时的初始化
    std::cout << "1. 主程序初始化阶段..." << std::endl;
    
    // 设置跨库访问器（如果需要）
    setSingletonRegistryAccessor([]() -> SingletonRegistry& {
        return SingletonRegistry::getInstance();
    });
    
    // 主程序统一注册所有需要跨库共享的单例
    auto& registry = singleton_registry();
    
    std::cout << "   注册核心单例..." << std::endl;
    registry.registerSingleton<ConfigLoader>();
    registry.registerSingleton<Logger>();
    // 可以注册更多需要跨库共享的单例...
    
    std::cout << "   ✅ 主程序注册完成，已注册 " << registry.size() << " 个单例类型" << std::endl;
    
    // 2. 主程序中使用单例
    std::cout << "2. 主程序使用单例..." << std::endl;
    
    auto main_config = getSingleton<ConfigLoader>();
    auto main_logger = getSingleton<Logger>();
    
    std::cout << "   主程序ConfigLoader地址: " << main_config.get() << std::endl;
    std::cout << "   主程序Logger地址: " << main_logger.get() << std::endl;
    
    auto console_logger = main_logger->getDefaultLogger();
    console_logger->info("主程序：单例获取成功");
    
    // 3. 模拟插件中的使用
    std::cout << "3. 模拟插件使用单例..." << std::endl;
    
    // 插件中也通过 singleton_registry() 访问
    auto& plugin_registry = singleton_registry();
    
    auto plugin_config = plugin_registry.get<ConfigLoader>();
    auto plugin_logger = plugin_registry.get<Logger>();
    
    std::cout << "   插件ConfigLoader地址: " << plugin_config.get() << std::endl;
    std::cout << "   插件Logger地址: " << plugin_logger.get() << std::endl;
    
    // 4. 验证单例唯一性
    std::cout << "4. 验证跨库单例唯一性..." << std::endl;
    
    if (main_config.get() == plugin_config.get()) {
        std::cout << "   ✅ ConfigLoader跨库唯一性保证" << std::endl;
    } else {
        std::cout << "   ❌ ConfigLoader跨库唯一性失败" << std::endl;
    }
    
    if (main_logger.get() == plugin_logger.get()) {
        std::cout << "   ✅ Logger跨库唯一性保证" << std::endl;
    } else {
        std::cout << "   ❌ Logger跨库唯一性失败" << std::endl;
    }
    
    auto plugin_console_logger = plugin_logger->getDefaultLogger();
    plugin_console_logger->info("插件：使用相同的Logger实例");
    
    // 5. 清理
    main_logger->shutdown();
    std::cout << "5. ✅ 清理完成" << std::endl;
}

void demonstrateKeyUniqueness() {
    std::cout << "\n=== typeid(T).name() 唯一性测试 ===" << std::endl;
    
    // 测试类型名称的唯一性
    std::string config_key = typeid(ConfigLoader).name();
    std::string logger_key = typeid(Logger).name();
    
    std::cout << "ConfigLoader类型名: " << config_key << std::endl;
    std::cout << "Logger类型名: " << logger_key << std::endl;
    
    std::cout << "\n⚠️  注意事项：" << std::endl;
    std::cout << "- typeid(T).name() 在同一进程内是唯一的" << std::endl;
    std::cout << "- 但在不同编译单元/动态库中可能有差异" << std::endl;
    std::cout << "- 因此需要主程序统一管理注册表" << std::endl;
    std::cout << "- 所有库都通过 singleton_registry() 访问同一实例" << std::endl;
}

int main() {
    try {
        demonstrateCorrectUsage();
        demonstrateKeyUniqueness();
        
        std::cout << "\n=== 跨动态库单例管理总结 ===" << std::endl;
        std::cout << "✅ 正确使用模式：" << std::endl;
        std::cout << "1. 主程序启动时统一注册所有需要跨库共享的单例" << std::endl;
        std::cout << "2. 设置跨库访问器，确保所有库使用同一注册表" << std::endl;
        std::cout << "3. 插件通过 singleton_registry() 访问主程序的注册表" << std::endl;
        std::cout << "4. 惰性初始化确保首次访问时才构造实例" << std::endl;
        
        std::cout << "\n🎯 关键要点：" << std::endl;
        std::cout << "- 主程序负责所有单例的注册" << std::endl;
        std::cout << "- 插件只能获取，不应该注册新的单例" << std::endl;
        std::cout << "- 通过统一的注册表保证跨库唯一性" << std::endl;
        std::cout << "- typeid(T).name() 在同一进程内保持一致" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "测试失败: " << e.what() << std::endl;
        return 1;
    }
}
